/**
 * OPTIMIZED Episodes and Seasons Management API Routes
 * Compatible with new database structure and automatic triggers
 * Senior Database Admin Solution for StreamDB
 */
const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireModerator } = require('../middleware/auth');

// =====================================================
// VALIDATION MIDDLEWARE (Minimal as per user requirements)
// =====================================================

const seasonValidation = [
  body('seasonNumber').optional().isInt({ min: 1 }).withMessage('Season number must be a positive integer'),
  body('title').optional().isLength({ max: 255 }).withMessage('Title must be less than 255 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('posterUrl').optional().custom((value) => {
    if (!value || value === '') return true; // Allow empty strings
    try {
      new URL(value);
      return true;
    } catch (error) {
      throw new Error('Poster URL must be a valid URL');
    }
  }),
];

const episodeValidation = [
  body('episodeNumber').optional().isInt({ min: 1 }).withMessage('Episode number must be a positive integer'),
  body('title').trim().isLength({ min: 1, max: 255 }).withMessage('Episode title is required and must be less than 255 characters'),
  body('secureVideoLinks').trim().isLength({ min: 1, max: 2000 }).withMessage('Video embed link is required and must be less than 2000 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('runtime').optional().isLength({ max: 20 }).withMessage('Runtime must be less than 20 characters'),
  body('airDate').optional().custom((value) => {
    if (!value || value === '') return true; // Allow empty strings
    if (isNaN(Date.parse(value))) throw new Error('Air date must be a valid date');
    return true;
  }),
  body('thumbnailUrl').optional().custom((value) => {
    if (!value || value === '') return true; // Allow empty strings
    try {
      new URL(value);
      return true;
    } catch (error) {
      throw new Error('Thumbnail URL must be a valid URL');
    }
  }),
];

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Get next available season number for content
 */
async function getNextSeasonNumber(contentId) {
  try {
    const [rows] = await db.execute(`
      SELECT COALESCE(MAX(season_number), 0) + 1 as next_season
      FROM seasons WHERE content_id = ?
    `, [contentId]);
    return rows[0]?.next_season || 1;
  } catch (error) {
    console.error('Error getting next season number:', error);
    return 1;
  }
}

/**
 * Get next available episode number for season
 */
async function getNextEpisodeNumber(seasonId) {
  try {
    const [rows] = await db.execute(`
      SELECT COALESCE(MAX(episode_number), 0) + 1 as next_episode
      FROM episodes WHERE season_id = ?
    `, [seasonId]);
    return rows[0]?.next_episode || 1;
  } catch (error) {
    console.error('Error getting next episode number:', error);
    return 1;
  }
}

/**
 * Verify content exists and is a series
 */
async function verifyContentExists(contentId) {
  const [rows] = await db.execute(`
    SELECT id, type FROM content WHERE id = ?
  `, [contentId]);
  
  if (rows.length === 0) {
    throw new Error('Content not found');
  }
  
  if (rows[0].type !== 'series') {
    throw new Error('Content must be a web series to have seasons');
  }
  
  return rows[0];
}

/**
 * Verify season exists
 */
async function verifySeasonExists(seasonId, contentId) {
  const [rows] = await db.execute(`
    SELECT id, season_number FROM seasons WHERE id = ? AND content_id = ?
  `, [seasonId, contentId]);
  
  if (rows.length === 0) {
    throw new Error('Season not found');
  }
  
  return rows[0];
}

// =====================================================
// SEASON ENDPOINTS
// =====================================================

/**
 * Get all seasons for a content item
 * GET /api/episodes/content/:contentId/seasons
 */
router.get('/content/:contentId/seasons', async (req, res) => {
  try {
    const { contentId } = req.params;

    // Verify content exists
    await verifyContentExists(contentId);

    // Get seasons with episode counts (using new episode_count field)
    const [seasons] = await db.execute(`
      SELECT s.*, 
             s.episode_count,
             COUNT(e.id) as actual_episode_count
      FROM seasons s
      LEFT JOIN episodes e ON s.id = e.season_id
      WHERE s.content_id = ?
      GROUP BY s.id
      ORDER BY s.season_number ASC
    `, [contentId]);

    // Get episodes for each season
    for (let season of seasons) {
      const [episodes] = await db.execute(`
        SELECT * FROM episodes
        WHERE season_id = ?
        ORDER BY episode_number ASC
      `, [season.id]);
      
      season.episodes = episodes;
    }

    res.json({
      success: true,
      data: seasons
    });
  } catch (error) {
    console.error('Error fetching seasons:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch seasons',
      error: error.message
    });
  }
});

/**
 * Create a new season
 * POST /api/episodes/content/:contentId/seasons
 */
router.post('/content/:contentId/seasons', authenticateToken, requireModerator, seasonValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { contentId } = req.params;
    let { seasonNumber, title, description, posterUrl } = req.body;

    // Verify content exists and is a series
    await verifyContentExists(contentId);

    // Auto-detect next season number if not provided
    if (!seasonNumber) {
      seasonNumber = await getNextSeasonNumber(contentId);
    }

    // Check if season number already exists
    const [existingSeasons] = await db.execute(`
      SELECT id FROM seasons WHERE content_id = ? AND season_number = ?
    `, [contentId, seasonNumber]);

    if (existingSeasons.length > 0) {
      // Auto-increment to next available
      seasonNumber = await getNextSeasonNumber(contentId);
    }

    // Generate unique season ID
    const seasonId = `season_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Insert season (triggers will automatically update content table)
    await db.execute(`
      INSERT INTO seasons (id, content_id, season_number, title, description, poster_url)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [seasonId, contentId, seasonNumber, title || null, description || null, posterUrl || null]);

    // Fetch the created season
    const [newSeasons] = await db.execute(`
      SELECT * FROM seasons WHERE id = ?
    `, [seasonId]);

    const newSeason = newSeasons[0];
    newSeason.episodes = [];

    res.status(201).json({
      success: true,
      message: 'Season created successfully',
      data: newSeason
    });
  } catch (error) {
    console.error('Error creating season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create season',
      error: error.message
    });
  }
});

/**
 * Update a season
 * PUT /api/episodes/content/:contentId/seasons/:seasonId
 */
router.put('/content/:contentId/seasons/:seasonId', authenticateToken, requireModerator, seasonValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { contentId, seasonId } = req.params;
    const { seasonNumber, title, description, posterUrl } = req.body;

    // Verify season exists
    await verifySeasonExists(seasonId, contentId);

    // If season number is being changed, check for conflicts
    if (seasonNumber) {
      const [existingSeasons] = await db.execute(`
        SELECT id FROM seasons WHERE content_id = ? AND season_number = ? AND id != ?
      `, [contentId, seasonNumber, seasonId]);

      if (existingSeasons.length > 0) {
        return res.status(400).json({
          success: false,
          message: `Season number ${seasonNumber} already exists for this content`
        });
      }
    }

    // Update season
    await db.execute(`
      UPDATE seasons
      SET season_number = COALESCE(?, season_number),
          title = ?,
          description = ?,
          poster_url = ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND content_id = ?
    `, [seasonNumber, title, description, posterUrl, seasonId, contentId]);

    // Fetch updated season
    const [updatedSeasons] = await db.execute(`
      SELECT * FROM seasons WHERE id = ?
    `, [seasonId]);

    res.json({
      success: true,
      message: 'Season updated successfully',
      data: updatedSeasons[0]
    });
  } catch (error) {
    console.error('Error updating season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update season',
      error: error.message
    });
  }
});

/**
 * Delete a season
 * DELETE /api/episodes/content/:contentId/seasons/:seasonId
 */
router.delete('/content/:contentId/seasons/:seasonId', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { contentId, seasonId } = req.params;

    // Verify season exists
    const season = await verifySeasonExists(seasonId, contentId);

    // Delete season (CASCADE will delete episodes, triggers will update content table)
    await db.execute(`
      DELETE FROM seasons WHERE id = ? AND content_id = ?
    `, [seasonId, contentId]);

    res.json({
      success: true,
      message: `Season ${season.season_number} deleted successfully`
    });
  } catch (error) {
    console.error('Error deleting season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete season',
      error: error.message
    });
  }
});

// =====================================================
// EPISODE ENDPOINTS
// =====================================================

/**
 * Create a new episode
 * POST /api/episodes/content/:contentId/seasons/:seasonId/episodes
 */
router.post('/content/:contentId/seasons/:seasonId/episodes', authenticateToken, requireModerator, episodeValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { contentId, seasonId } = req.params;
    let { episodeNumber, title, description, secureVideoLinks, runtime, airDate, thumbnailUrl } = req.body;

    // Verify content and season exist
    await verifyContentExists(contentId);
    await verifySeasonExists(seasonId, contentId);

    // Auto-detect next episode number if not provided
    if (!episodeNumber) {
      episodeNumber = await getNextEpisodeNumber(seasonId);
    }

    // Check if episode number already exists in this season
    const [existingEpisodes] = await db.execute(`
      SELECT id FROM episodes WHERE season_id = ? AND episode_number = ?
    `, [seasonId, episodeNumber]);

    if (existingEpisodes.length > 0) {
      // Auto-increment to next available
      episodeNumber = await getNextEpisodeNumber(seasonId);
    }

    // Generate unique episode ID
    const episodeId = `episode_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Insert episode (triggers will automatically update season and content tables)
    await db.execute(`
      INSERT INTO episodes (id, season_id, content_id, episode_number, title, description, secure_video_links, runtime, air_date, thumbnail_url)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [episodeId, seasonId, contentId, episodeNumber, title, description || null, secureVideoLinks, runtime || null, airDate || null, thumbnailUrl || null]);

    // Fetch the created episode
    const [newEpisodes] = await db.execute(`
      SELECT * FROM episodes WHERE id = ?
    `, [episodeId]);

    res.status(201).json({
      success: true,
      message: 'Episode created successfully',
      data: newEpisodes[0]
    });
  } catch (error) {
    console.error('Error creating episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create episode',
      error: error.message
    });
  }
});

module.exports = router;
