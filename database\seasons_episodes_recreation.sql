-- =====================================================
-- SEASONS & EPISODES DATABASE RECREATION SCRIPT
-- Senior Database Admin Solution for StreamDB
-- =====================================================
-- This script safely recreates the seasons and episodes tables
-- with proper relationships, constraints, and automatic triggers
-- to sync with the content table.

-- =====================================================
-- STEP 1: BACKUP EXISTING DATA (if any)
-- =====================================================

-- Create temporary backup tables
CREATE TABLE IF NOT EXISTS seasons_backup AS SELECT * FROM seasons WHERE 1=0;
CREATE TABLE IF NOT EXISTS episodes_backup AS SELECT * FROM episodes WHERE 1=0;

-- Backup existing data if tables exist
INSERT IGNORE INTO seasons_backup SELECT * FROM seasons;
INSERT IGNORE INTO episodes_backup SELECT * FROM episodes;

-- =====================================================
-- STEP 2: DROP EXISTING TABLES (CASCADE)
-- =====================================================

-- Disable foreign key checks temporarily for clean recreation
SET FOREIGN_KEY_CHECKS = 0;

-- Drop existing tables in correct order (episodes first due to FK)
DROP TABLE IF EXISTS episodes;
DROP TABLE IF EXISTS seasons;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- STEP 3: CREATE NEW SEASONS TABLE
-- =====================================================

CREATE TABLE seasons (
    -- Primary identification
    id VARCHAR(50) PRIMARY KEY COMMENT 'Unique season identifier',
    content_id VARCHAR(50) NOT NULL COMMENT 'Reference to parent web series',
    season_number INT NOT NULL COMMENT 'Season number (1, 2, 3, etc.)',
    
    -- Optional metadata (as per user requirements)
    title VARCHAR(255) NULL COMMENT 'Optional season title',
    description TEXT NULL COMMENT 'Optional season description', 
    poster_url VARCHAR(500) NULL COMMENT 'Optional season poster image',
    
    -- Episode tracking
    episode_count INT DEFAULT 0 COMMENT 'Auto-calculated episode count',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
    
    -- Constraints and relationships
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE KEY unique_season_per_content (content_id, season_number),
    
    -- Performance indexes
    INDEX idx_content_id (content_id),
    INDEX idx_season_number (season_number),
    INDEX idx_content_season (content_id, season_number),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='Web series seasons with minimal required fields';

-- =====================================================
-- STEP 4: CREATE NEW EPISODES TABLE  
-- =====================================================

CREATE TABLE episodes (
    -- Primary identification
    id VARCHAR(50) PRIMARY KEY COMMENT 'Unique episode identifier',
    season_id VARCHAR(50) NOT NULL COMMENT 'Reference to parent season',
    content_id VARCHAR(50) NOT NULL COMMENT 'Reference to parent web series',
    episode_number INT NOT NULL COMMENT 'Episode number within season',
    
    -- Required fields (as per user requirements)
    title VARCHAR(255) NOT NULL COMMENT 'Episode title (required)',
    secure_video_links TEXT NOT NULL COMMENT 'Video embed link (required)',
    
    -- Optional metadata
    description TEXT NULL COMMENT 'Optional episode description',
    runtime VARCHAR(20) NULL COMMENT 'Optional episode runtime',
    air_date DATE NULL COMMENT 'Optional air date',
    thumbnail_url VARCHAR(500) NULL COMMENT 'Optional episode thumbnail',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Creation timestamp',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last update timestamp',
    
    -- Constraints and relationships
    FOREIGN KEY (season_id) REFERENCES seasons(id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE ON UPDATE CASCADE,
    UNIQUE KEY unique_episode_per_season (season_id, episode_number),
    
    -- Performance indexes
    INDEX idx_season_id (season_id),
    INDEX idx_content_id (content_id),
    INDEX idx_episode_number (episode_number),
    INDEX idx_season_episode (season_id, episode_number),
    INDEX idx_content_episode (content_id, episode_number),
    INDEX idx_created_at (created_at),
    INDEX idx_air_date (air_date)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='Web series episodes with minimal required fields';

-- =====================================================
-- STEP 5: CREATE AUTOMATIC TRIGGERS FOR SYNC
-- =====================================================

-- Trigger to update episode count in seasons table
DELIMITER $$

CREATE TRIGGER update_season_episode_count_after_insert
AFTER INSERT ON episodes
FOR EACH ROW
BEGIN
    UPDATE seasons
    SET episode_count = (
        SELECT COUNT(*) FROM episodes WHERE season_id = NEW.season_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.season_id;
END$$

CREATE TRIGGER update_season_episode_count_after_delete
AFTER DELETE ON episodes
FOR EACH ROW
BEGIN
    UPDATE seasons
    SET episode_count = (
        SELECT COUNT(*) FROM episodes WHERE season_id = OLD.season_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.season_id;
END$$

-- Trigger to update total_seasons and total_episodes in content table
CREATE TRIGGER update_content_totals_after_season_insert
AFTER INSERT ON seasons
FOR EACH ROW
BEGIN
    UPDATE content
    SET total_seasons = (
        SELECT COUNT(*) FROM seasons WHERE content_id = NEW.content_id
    ),
    total_episodes = (
        SELECT COUNT(*) FROM episodes WHERE content_id = NEW.content_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.content_id;
END$$

CREATE TRIGGER update_content_totals_after_season_delete
AFTER DELETE ON seasons
FOR EACH ROW
BEGIN
    UPDATE content
    SET total_seasons = (
        SELECT COUNT(*) FROM seasons WHERE content_id = OLD.content_id
    ),
    total_episodes = (
        SELECT COUNT(*) FROM episodes WHERE content_id = OLD.content_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.content_id;
END$$

CREATE TRIGGER update_content_totals_after_episode_insert
AFTER INSERT ON episodes
FOR EACH ROW
BEGIN
    UPDATE content
    SET total_episodes = (
        SELECT COUNT(*) FROM episodes WHERE content_id = NEW.content_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.content_id;
END$$

CREATE TRIGGER update_content_totals_after_episode_delete
AFTER DELETE ON episodes
FOR EACH ROW
BEGIN
    UPDATE content
    SET total_episodes = (
        SELECT COUNT(*) FROM episodes WHERE content_id = OLD.content_id
    ),
    updated_at = CURRENT_TIMESTAMP
    WHERE id = OLD.content_id;
END$$

DELIMITER ;

-- =====================================================
-- STEP 6: RESTORE DATA (if backup exists)
-- =====================================================

-- Restore seasons data if backup exists
INSERT IGNORE INTO seasons (id, content_id, season_number, title, description, poster_url, created_at, updated_at)
SELECT id, content_id, season_number, title, description, poster_url, created_at, updated_at
FROM seasons_backup;

-- Restore episodes data if backup exists
INSERT IGNORE INTO episodes (id, season_id, content_id, episode_number, title, description, secure_video_links, runtime, air_date, thumbnail_url, created_at, updated_at)
SELECT id, season_id, content_id, episode_number, title, description,
       COALESCE(secure_video_links, video_link) as secure_video_links, -- Handle legacy video_link field
       runtime, air_date, thumbnail_url, created_at, updated_at
FROM episodes_backup;

-- =====================================================
-- STEP 7: CLEANUP AND VERIFICATION
-- =====================================================

-- Drop backup tables
DROP TABLE IF EXISTS seasons_backup;
DROP TABLE IF EXISTS episodes_backup;

-- Verify table structure
SHOW CREATE TABLE seasons;
SHOW CREATE TABLE episodes;

-- Verify triggers
SHOW TRIGGERS LIKE 'seasons';
SHOW TRIGGERS LIKE 'episodes';

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'SEASONS & EPISODES TABLES RECREATED SUCCESSFULLY!' as STATUS,
       'Tables now have proper relationships, constraints, and automatic sync triggers' as DETAILS;
