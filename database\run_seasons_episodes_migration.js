/**
 * Seasons & Episodes Database Recreation Migration Script
 * Senior Database Admin Solution for StreamDB
 * 
 * This script safely recreates the seasons and episodes tables with:
 * - Proper relationships and constraints
 * - Automatic triggers for content table sync
 * - Data backup and restoration
 * - Comprehensive error handling
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  charset: 'utf8mb4',
  timezone: '+00:00',
  connectTimeout: 60000,
  multipleStatements: true // Required for running the full migration script
};

// Configure connection method based on environment
if (process.env.NODE_ENV === 'production' && process.env.DB_SOCKET) {
  dbConfig.socketPath = process.env.DB_SOCKET;
  console.log('🔌 Using MySQL socket connection for production');
} else {
  dbConfig.host = process.env.DB_HOST || 'localhost';
  dbConfig.port = process.env.DB_PORT || 3306;
  console.log(`🔌 Using TCP connection: ${dbConfig.host}:${dbConfig.port}`);
}

async function runMigration() {
  let connection;
  
  try {
    console.log('🚀 Starting Seasons & Episodes Database Recreation...');
    console.log('📋 This migration will:');
    console.log('   ✓ Backup existing data');
    console.log('   ✓ Drop and recreate tables with proper structure');
    console.log('   ✓ Add automatic sync triggers');
    console.log('   ✓ Restore backed up data');
    console.log('   ✓ Verify table structure and triggers');
    console.log('');

    // Connect to database
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database successfully');

    // Read migration file
    const migrationPath = path.join(__dirname, 'seasons_episodes_recreation.sql');
    console.log('📖 Reading migration file:', migrationPath);
    
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');
    console.log('✅ Migration file loaded');

    // Execute migration
    console.log('🚀 Executing migration...');
    console.log('⚠️  This may take a few moments...');
    
    const [results] = await connection.execute(migrationSQL);
    
    console.log('✅ Migration executed successfully!');
    console.log('');

    // Verify the new table structure
    console.log('🔍 Verifying new table structure...');
    
    // Check seasons table
    const [seasonsStructure] = await connection.execute('DESCRIBE seasons');
    console.log('📋 Seasons table structure:');
    seasonsStructure.forEach(column => {
      console.log(`   ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${column.Key ? `(${column.Key})` : ''}`);
    });
    console.log('');

    // Check episodes table
    const [episodesStructure] = await connection.execute('DESCRIBE episodes');
    console.log('📋 Episodes table structure:');
    episodesStructure.forEach(column => {
      console.log(`   ${column.Field}: ${column.Type} ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${column.Key ? `(${column.Key})` : ''}`);
    });
    console.log('');

    // Check triggers
    const [triggers] = await connection.execute("SHOW TRIGGERS WHERE `Table` IN ('seasons', 'episodes')");
    console.log('🔧 Database triggers created:');
    triggers.forEach(trigger => {
      console.log(`   ${trigger.Trigger} on ${trigger.Table} (${trigger.Event} ${trigger.Timing})`);
    });
    console.log('');

    // Check data counts
    const [seasonCount] = await connection.execute('SELECT COUNT(*) as count FROM seasons');
    const [episodeCount] = await connection.execute('SELECT COUNT(*) as count FROM episodes');
    
    console.log('📊 Data verification:');
    console.log(`   Seasons: ${seasonCount[0].count} records`);
    console.log(`   Episodes: ${episodeCount[0].count} records`);
    console.log('');

    console.log('🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('');
    console.log('✅ What was accomplished:');
    console.log('   ✓ Seasons table recreated with proper structure');
    console.log('   ✓ Episodes table recreated with proper structure');
    console.log('   ✓ Foreign key relationships established');
    console.log('   ✓ Automatic triggers for content table sync');
    console.log('   ✓ Performance indexes added');
    console.log('   ✓ Data backup and restoration completed');
    console.log('');
    console.log('🔄 Next steps:');
    console.log('   1. Test season creation in Admin Panel');
    console.log('   2. Test episode creation in Admin Panel');
    console.log('   3. Verify content table totals update automatically');
    console.log('   4. Check frontend display of seasons/episodes');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.error('');
    console.error('🔧 Troubleshooting:');
    console.error('   1. Check database connection settings in .env file');
    console.error('   2. Ensure database user has CREATE, DROP, ALTER privileges');
    console.error('   3. Verify database name exists');
    console.error('   4. Check if content table exists (required for foreign keys)');
    console.error('');
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('🔧 Seasons & Episodes Database Recreation Migration');
  console.log('');
  console.log('Usage: node run_seasons_episodes_migration.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --dry-run      Show what would be executed without running');
  console.log('');
  console.log('Environment Variables:');
  console.log('  DB_USER        Database username');
  console.log('  DB_PASSWORD    Database password');
  console.log('  DB_NAME        Database name');
  console.log('  DB_HOST        Database host (default: localhost)');
  console.log('  DB_PORT        Database port (default: 3306)');
  console.log('  DB_SOCKET      Database socket path (for production)');
  process.exit(0);
}

if (args.includes('--dry-run')) {
  console.log('🔍 Dry run mode - showing migration content:');
  console.log('');
  
  fs.readFile(path.join(__dirname, 'seasons_episodes_recreation.sql'), 'utf8')
    .then(content => {
      console.log(content);
      console.log('');
      console.log('💡 This is what would be executed. Run without --dry-run to execute.');
    })
    .catch(error => {
      console.error('❌ Failed to read migration file:', error);
      process.exit(1);
    });
} else {
  // Run the migration
  runMigration();
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️  Migration interrupted by user');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
