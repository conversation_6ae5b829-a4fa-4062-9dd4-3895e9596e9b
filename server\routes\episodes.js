/**
 * Episodes and Seasons Management API Routes
 * Handles CRUD operations for episodes and seasons of web series
 * ULTIMATE FIX: Auto-detects next season/episode numbers, minimal validation
 */
const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireModerator } = require('../middleware/auth');

// MINIMAL validation for seasons - only season number required, everything else optional
const seasonValidation = [
  body('seasonNumber').optional().isInt({ min: 1 }).withMessage('Season number must be a positive integer'),
  body('title').optional().isLength({ max: 255 }).withMessage('Title must be less than 255 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('posterUrl').optional().custom((value) => {
    if (!value || value === '') {
      return true; // Allow empty strings
    }
    try {
      new URL(value);
      return true;
    } catch (error) {
      throw new Error('Poster URL must be a valid URL');
    }
  }),
];

// MINIMAL validation for episodes - only title and video links required
const episodeValidation = [
  body('episodeNumber').optional().isInt({ min: 1 }).withMessage('Episode number must be a positive integer'),
  body('title').trim().isLength({ min: 1, max: 255 }).withMessage('Episode title is required and must be less than 255 characters'),
  body('secureVideoLinks').trim().isLength({ min: 1, max: 2000 }).withMessage('Video embed link is required and must be less than 2000 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('runtime').optional().isLength({ max: 20 }).withMessage('Runtime must be less than 20 characters'),
  body('airDate').optional().isISO8601().withMessage('Air date must be a valid date'),
  body('thumbnailUrl').optional().custom((value) => {
    if (!value || value === '') {
      return true; // Allow empty strings
    }
    try {
      new URL(value);
      return true;
    } catch (error) {
      throw new Error('Thumbnail URL must be a valid URL');
    }
  }),
];

/**
 * Get next available season number for content
 */
async function getNextSeasonNumber(contentId) {
  try {
    const result = await db.execute(`
      SELECT MAX(season_number) as max_season FROM seasons WHERE content_id = ?
    `, [contentId]);
    
    const maxSeason = (result && result[0] && result[0][0] && result[0][0].max_season) ? result[0][0].max_season : 0;
    return maxSeason + 1;
  } catch (error) {
    console.error('Error getting next season number:', error);
    return 1; // Default to season 1 if error
  }
}

/**
 * Get next available episode number for season
 */
async function getNextEpisodeNumber(seasonId) {
  try {
    const result = await db.execute(`
      SELECT MAX(episode_number) as max_episode FROM episodes WHERE season_id = ?
    `, [seasonId]);
    
    const maxEpisode = (result && result[0] && result[0][0] && result[0][0].max_episode) ? result[0][0].max_episode : 0;
    return maxEpisode + 1;
  } catch (error) {
    console.error('Error getting next episode number:', error);
    return 1; // Default to episode 1 if error
  }
}

/**
 * Get all seasons for a content item
 * GET /api/episodes/content/:contentId/seasons
 */
router.get('/content/:contentId/seasons', async (req, res) => {
  try {
    const { contentId } = req.params;

    const result = await db.execute(`
      SELECT * FROM seasons
      WHERE content_id = ?
      ORDER BY season_number ASC
    `, [contentId]);

    const seasons = (result && result[0]) ? result[0] : [];

    // Get episodes for each season
    for (let season of seasons) {
      const episodeResult = await db.execute(`
        SELECT * FROM episodes
        WHERE season_id = ?
        ORDER BY episode_number ASC
      `, [season.id]);
      
      season.episodes = (episodeResult && episodeResult[0]) ? episodeResult[0] : [];
    }

    res.json({
      success: true,
      data: seasons
    });
  } catch (error) {
    console.error('Error fetching seasons:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch seasons',
      error: error.message
    });
  }
});

/**
 * Create a new season
 * POST /api/episodes/content/:contentId/seasons
 * AUTO-DETECTS next season number if not provided
 */
router.post('/content/:contentId/seasons', authenticateToken, requireModerator, seasonValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Season validation errors:', errors.array());
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { contentId } = req.params;
    let { seasonNumber, title, description, posterUrl } = req.body;

    // Check if content exists
    const contentResult = await db.execute(`
      SELECT id FROM content WHERE id = ?
    `, [contentId]);

    const contentRows = (contentResult && contentResult[0]) ? contentResult[0] : [];
    
    if (contentRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }

    // AUTO-DETECT next season number if not provided
    if (!seasonNumber) {
      seasonNumber = await getNextSeasonNumber(contentId);
      console.log(`Auto-detected next season number: ${seasonNumber}`);
    }

    // Check if season number already exists for this content
    const existingResult = await db.execute(`
      SELECT id FROM seasons WHERE content_id = ? AND season_number = ?
    `, [contentId, seasonNumber]);

    const existingSeasons = (existingResult && existingResult[0]) ? existingResult[0] : [];

    if (existingSeasons.length > 0) {
      // If season exists, auto-increment to next available
      seasonNumber = await getNextSeasonNumber(contentId);
      console.log(`Season ${req.body.seasonNumber} exists, using next available: ${seasonNumber}`);
    }

    // Generate season ID
    const seasonId = `season_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Auto-generate title if not provided
    if (!title || title.trim() === '') {
      title = `Season ${seasonNumber}`;
    }

    // Insert season
    await db.execute(`
      INSERT INTO seasons (id, content_id, season_number, title, description, poster_url, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [seasonId, contentId, seasonNumber, title, description || null, posterUrl || null]);

    // Fetch the created season
    const newSeasonResult = await db.execute(`
      SELECT * FROM seasons WHERE id = ?
    `, [seasonId]);

    const newSeasonRows = (newSeasonResult && newSeasonResult[0]) ? newSeasonResult[0] : [];
    const newSeason = newSeasonRows[0];
    newSeason.episodes = [];

    res.status(201).json({
      success: true,
      message: 'Season created successfully',
      data: newSeason
    });
  } catch (error) {
    console.error('Error creating season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create season',
      error: error.message
    });
  }
});

/**
 * Get a specific season with episodes
 * GET /api/episodes/content/:contentId/seasons/:seasonId
 */
router.get('/content/:contentId/seasons/:seasonId', async (req, res) => {
  try {
    const { contentId, seasonId } = req.params;

    const seasonResult = await db.execute(`
      SELECT * FROM seasons
      WHERE id = ? AND content_id = ?
    `, [seasonId, contentId]);

    const seasonRows = (seasonResult && seasonResult[0]) ? seasonResult[0] : [];

    if (seasonRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Season not found'
      });
    }

    const season = seasonRows[0];

    // Get episodes for this season
    const episodesResult = await db.execute(`
      SELECT * FROM episodes
      WHERE season_id = ?
      ORDER BY episode_number ASC
    `, [seasonId]);

    season.episodes = (episodesResult && episodesResult[0]) ? episodesResult[0] : [];

    res.json({
      success: true,
      data: season
    });
  } catch (error) {
    console.error('Error fetching season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch season',
      error: error.message
    });
  }
});

/**
 * Update a season
 * PUT /api/episodes/content/:contentId/seasons/:seasonId
 */
router.put('/content/:contentId/seasons/:seasonId', authenticateToken, requireModerator, seasonValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { contentId, seasonId } = req.params;
    const { seasonNumber, title, description, posterUrl } = req.body;

    // Check if season exists
    const existingResult = await db.execute(`
      SELECT id FROM seasons WHERE id = ? AND content_id = ?
    `, [seasonId, contentId]);

    const existingSeasons = (existingResult && existingResult[0]) ? existingResult[0] : [];

    if (existingSeasons.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Season not found'
      });
    }

    // Check if season number conflicts with another season
    if (seasonNumber) {
      const conflictResult = await db.execute(`
        SELECT id FROM seasons WHERE content_id = ? AND season_number = ? AND id != ?
      `, [contentId, seasonNumber, seasonId]);

      const conflictingSeasons = (conflictResult && conflictResult[0]) ? conflictResult[0] : [];

      if (conflictingSeasons.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Season number already exists for this content'
        });
      }
    }

    // Update season
    await db.execute(`
      UPDATE seasons 
      SET season_number = ?, title = ?, description = ?, poster_url = ?, updated_at = NOW()
      WHERE id = ?
    `, [seasonNumber || null, title || null, description || null, posterUrl || null, seasonId]);

    // Fetch updated season
    const updatedResult = await db.execute(`
      SELECT * FROM seasons WHERE id = ?
    `, [seasonId]);

    const updatedSeasonRows = (updatedResult && updatedResult[0]) ? updatedResult[0] : [];
    const updatedSeason = updatedSeasonRows[0];

    // Get episodes for this season
    const episodesResult = await db.execute(`
      SELECT * FROM episodes WHERE season_id = ? ORDER BY episode_number ASC
    `, [seasonId]);

    updatedSeason.episodes = (episodesResult && episodesResult[0]) ? episodesResult[0] : [];

    res.json({
      success: true,
      message: 'Season updated successfully',
      data: updatedSeason
    });
  } catch (error) {
    console.error('Error updating season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update season',
      error: error.message
    });
  }
});

/**
 * Delete a season and all its episodes
 * DELETE /api/episodes/content/:contentId/seasons/:seasonId
 */
router.delete('/content/:contentId/seasons/:seasonId', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { contentId, seasonId } = req.params;

    // Check if season exists
    const existingResult = await db.execute(`
      SELECT id FROM seasons WHERE id = ? AND content_id = ?
    `, [seasonId, contentId]);

    const existingSeasons = (existingResult && existingResult[0]) ? existingResult[0] : [];

    if (existingSeasons.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Season not found'
      });
    }

    // Delete all episodes in this season first
    await db.execute(`
      DELETE FROM episodes WHERE season_id = ?
    `, [seasonId]);

    // Delete the season
    await db.execute(`
      DELETE FROM seasons WHERE id = ?
    `, [seasonId]);

    res.json({
      success: true,
      message: 'Season and all its episodes deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete season',
      error: error.message
    });
  }
});

/**
 * Create a new episode in a season
 * POST /api/episodes/content/:contentId/seasons/:seasonId/episodes
 * AUTO-DETECTS next episode number if not provided
 */
router.post('/content/:contentId/seasons/:seasonId/episodes', authenticateToken, requireModerator, episodeValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { contentId, seasonId } = req.params;
    let { episodeNumber, title, description, secureVideoLinks, runtime, airDate, thumbnailUrl } = req.body;

    // Check if season exists
    const seasonResult = await db.execute(`
      SELECT id FROM seasons WHERE id = ? AND content_id = ?
    `, [seasonId, contentId]);

    const seasonRows = (seasonResult && seasonResult[0]) ? seasonResult[0] : [];

    if (seasonRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Season not found'
      });
    }

    // AUTO-DETECT next episode number if not provided
    if (!episodeNumber) {
      episodeNumber = await getNextEpisodeNumber(seasonId);
      console.log(`Auto-detected next episode number: ${episodeNumber}`);
    }

    // Check if episode number already exists in this season
    const existingResult = await db.execute(`
      SELECT id FROM episodes WHERE season_id = ? AND episode_number = ?
    `, [seasonId, episodeNumber]);

    const existingEpisodes = (existingResult && existingResult[0]) ? existingResult[0] : [];

    if (existingEpisodes.length > 0) {
      // If episode exists, auto-increment to next available
      episodeNumber = await getNextEpisodeNumber(seasonId);
      console.log(`Episode ${req.body.episodeNumber} exists, using next available: ${episodeNumber}`);
    }

    // Generate episode ID
    const episodeId = `episode_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Insert episode
    await db.execute(`
      INSERT INTO episodes (id, season_id, content_id, episode_number, title, description, secure_video_links, runtime, air_date, thumbnail_url, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [episodeId, seasonId, contentId, episodeNumber, title, description || null, secureVideoLinks, runtime || null, airDate || null, thumbnailUrl || null]);

    // Fetch the created episode
    const newEpisodeResult = await db.execute(`
      SELECT * FROM episodes WHERE id = ?
    `, [episodeId]);

    const newEpisodeRows = (newEpisodeResult && newEpisodeResult[0]) ? newEpisodeResult[0] : [];

    res.status(201).json({
      success: true,
      message: 'Episode created successfully',
      data: newEpisodeRows[0]
    });
  } catch (error) {
    console.error('Error creating episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create episode',
      error: error.message
    });
  }
});

/**
 * Get all episodes in a season
 * GET /api/episodes/content/:contentId/seasons/:seasonId/episodes
 */
router.get('/content/:contentId/seasons/:seasonId/episodes', async (req, res) => {
  try {
    const { contentId, seasonId } = req.params;

    // Check if season exists
    const seasonResult = await db.execute(`
      SELECT id FROM seasons WHERE id = ? AND content_id = ?
    `, [seasonId, contentId]);

    const seasonRows = (seasonResult && seasonResult[0]) ? seasonResult[0] : [];

    if (seasonRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Season not found'
      });
    }

    // Get episodes
    const episodesResult = await db.execute(`
      SELECT * FROM episodes
      WHERE season_id = ?
      ORDER BY episode_number ASC
    `, [seasonId]);

    const episodesRows = (episodesResult && episodesResult[0]) ? episodesResult[0] : [];

    res.json({
      success: true,
      data: episodesRows
    });
  } catch (error) {
    console.error('Error fetching episodes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch episodes',
      error: error.message
    });
  }
});

/**
 * Get a specific episode
 * GET /api/episodes/content/:contentId/seasons/:seasonId/episodes/:episodeId
 */
router.get('/content/:contentId/seasons/:seasonId/episodes/:episodeId', async (req, res) => {
  try {
    const { contentId, seasonId, episodeId } = req.params;

    const episodeResult = await db.execute(`
      SELECT e.*, s.content_id 
      FROM episodes e
      JOIN seasons s ON e.season_id = s.id
      WHERE e.id = ? AND e.season_id = ? AND s.content_id = ?
    `, [episodeId, seasonId, contentId]);

    const episodeRows = (episodeResult && episodeResult[0]) ? episodeResult[0] : [];

    if (episodeRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Episode not found'
      });
    }

    res.json({
      success: true,
      data: episodeRows[0]
    });
  } catch (error) {
    console.error('Error fetching episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch episode',
      error: error.message
    });
  }
});

/**
 * Update an episode
 * PUT /api/episodes/content/:contentId/seasons/:seasonId/episodes/:episodeId
 */
router.put('/content/:contentId/seasons/:seasonId/episodes/:episodeId', authenticateToken, requireModerator, episodeValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { contentId, seasonId, episodeId } = req.params;
    const { episodeNumber, title, description, secureVideoLinks, runtime, airDate, thumbnailUrl } = req.body;

    // Check if episode exists
    const existingResult = await db.execute(`
      SELECT e.id 
      FROM episodes e
      JOIN seasons s ON e.season_id = s.id
      WHERE e.id = ? AND e.season_id = ? AND s.content_id = ?
    `, [episodeId, seasonId, contentId]);

    const existingEpisodes = (existingResult && existingResult[0]) ? existingResult[0] : [];

    if (existingEpisodes.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Episode not found'
      });
    }

    // Check if episode number conflicts with another episode in the same season
    if (episodeNumber) {
      const conflictResult = await db.execute(`
        SELECT id FROM episodes WHERE season_id = ? AND episode_number = ? AND id != ?
      `, [seasonId, episodeNumber, episodeId]);

      const conflictingEpisodes = (conflictResult && conflictResult[0]) ? conflictResult[0] : [];

      if (conflictingEpisodes.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Episode number already exists in this season'
        });
      }
    }

    // Update episode
    await db.execute(`
      UPDATE episodes 
      SET episode_number = ?, title = ?, description = ?, secure_video_links = ?, runtime = ?, air_date = ?, thumbnail_url = ?, updated_at = NOW()
      WHERE id = ?
    `, [episodeNumber || null, title, description || null, secureVideoLinks, runtime || null, airDate || null, thumbnailUrl || null, episodeId]);

    // Fetch updated episode
    const updatedResult = await db.execute(`
      SELECT * FROM episodes WHERE id = ?
    `, [episodeId]);

    const updatedEpisodeRows = (updatedResult && updatedResult[0]) ? updatedResult[0] : [];

    res.json({
      success: true,
      message: 'Episode updated successfully',
      data: updatedEpisodeRows[0]
    });
  } catch (error) {
    console.error('Error updating episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update episode',
      error: error.message
    });
  }
});

/**
 * Delete an episode
 * DELETE /api/episodes/content/:contentId/seasons/:seasonId/episodes/:episodeId
 */
router.delete('/content/:contentId/seasons/:seasonId/episodes/:episodeId', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { contentId, seasonId, episodeId } = req.params;

    // Check if episode exists
    const existingResult = await db.execute(`
      SELECT e.id 
      FROM episodes e
      JOIN seasons s ON e.season_id = s.id
      WHERE e.id = ? AND e.season_id = ? AND s.content_id = ?
    `, [episodeId, seasonId, contentId]);

    const existingEpisodes = (existingResult && existingResult[0]) ? existingResult[0] : [];

    if (existingEpisodes.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Episode not found'
      });
    }

    // Delete the episode
    await db.execute(`
      DELETE FROM episodes WHERE id = ?
    `, [episodeId]);

    res.json({
      success: true,
      message: 'Episode deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete episode',
      error: error.message
    });
  }
});

module.exports = router;
